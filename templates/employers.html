{% extends "base.html" %} {% block title %}Employers{% endblock %} {% block content %}

<!-- Hero Section -->
<section class="employers-hero-bg py-5">
  <div class="container">
    <div class="row align-items-center min-vh-40">
      <div class="col-lg-8 mx-auto text-center">
        <div class="hero-content">
          <h1 class="employers-hero-title mb-4">
            Discover
            <span class="hero-gradient-text">Leading Companies</span>
            <br />Shaping Tomorrow
          </h1>
          <p class="employers-hero-subtitle mb-5">
            Connect with innovative organizations, explore company cultures, and find your perfect workplace match.
            From startups to Fortune 500 companies, discover opportunities that align with your career goals.
          </p>

          <!-- Hero Stats -->
          <div class="hero-stats-employers d-flex justify-content-center gap-5 mb-5">
            <div class="stat-item-employers">
              <div class="stat-number-employers">{{ all_employers|length }}+</div>
              <div class="stat-label-employers">Companies</div>
            </div>
            <div class="stat-item-employers">
              <div class="stat-number-employers">{{ total_open_positions }}+</div>
              <div class="stat-label-employers">Open Positions</div>
            </div>
            <div class="stat-item-employers">
              <div class="stat-number-employers">50+</div>
              <div class="stat-label-employers">Industries</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Floating Elements -->
  <div class="floating-elements-employers">
    <div class="floating-card-employers floating-card-employers-1">
      <i class="bi bi-rocket-takeoff"></i>
      <span>Innovation</span>
    </div>
    <div class="floating-card-employers floating-card-employers-2">
      <i class="bi bi-people"></i>
      <span>Great Teams</span>
    </div>
    <div class="floating-card-employers floating-card-employers-3">
      <i class="bi bi-trophy"></i>
      <span>Top Rated</span>
    </div>
  </div>
</section>

<!-- Search Section -->
<section class="employers-search-section py-5">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="employers-search-card">
          <div class="search-header text-center mb-4">
            <h3 class="search-title">Find Your Next Employer</h3>
            <p class="search-subtitle">Search by company name, industry, location, or company size</p>
          </div>

          <div class="employers-search-wrapper">
            <div class="employers-search-input-group">
              <i class="bi bi-search employers-search-icon"></i>
              <input
                type="text"
                class="employers-search-input"
                id="keyword-input"
                name="keyword-input"
                placeholder="Search companies, industries, locations..."
                hx-get="/filteremp"
                hx-target="#employers-list"
                hx-trigger="keyup changed delay:300ms"
                hx-indicator="#search-loading"
              />
              <button type="button" class="employers-search-btn">
                <i class="bi bi-arrow-right"></i>
              </button>
            </div>

            <!-- Search Loading Indicator -->
            <div class="text-center mt-3">
              <div id="search-loading" class="htmx-indicator">
                <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <span class="text-muted">Searching companies...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Companies Section -->
<section class="employers-listing-section py-5">
  <div class="container">

    <!-- Section Header -->
    <div class="section-header text-center mb-5">
      <h2 class="section-title">
        <span class="gradient-text">Featured Companies</span>
      </h2>
      <p class="section-subtitle">
        Explore opportunities with companies that are actively hiring and growing
      </p>
      <div class="section-divider"></div>
    </div>

    <!-- Employers Grid -->
    <div id="employers-list" class="employers-grid">
      {% for employer in all_employers %}
      <div class="employer-card-ultra-modern" data-aos="fade-up" data-aos-delay="{{ loop.index0 * 100 }}">
        <a href="{{url_for('employer',employer_id=employer.employer_id)}}" class="employer-link">
          <!-- Card Header with Logo -->
          <div class="employer-card-header-modern">
            <div class="employer-logo-container-modern">
              {% if employer.employer_logo_url %}
              <img
                src="{{ employer.employer_logo_url }}"
                class="employer-logo-modern"
                alt="{{ employer.employer_name }}"
                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
              />
              {% endif %}
              <div class="employer-logo-fallback-modern" {% if employer.employer_logo_url %}style="display: none"{% endif %}>
                {{ (employer.employer_name or 'C')[0]|upper }}
              </div>
            </div>

            <!-- Open Positions Badge -->
            {% if employer.open_positions > 0 %}
            <div class="positions-badge">
              <i class="bi bi-briefcase me-1"></i>
              {{ employer.open_positions }} {% if employer.open_positions == 1 %}Position{% else %}Positions{% endif %}
            </div>
            {% endif %}
          </div>

          <!-- Company Info -->
          <div class="employer-info-modern">
            <h3 class="company-name-modern">{{ employer.employer_name }}</h3>

            <!-- Company Details Grid -->
            <div class="company-details-grid">
              {% if employer.employer_industry %}
              <div class="detail-item-modern">
                <div class="detail-icon-modern">
                  <i class="bi bi-diagram-3"></i>
                </div>
                <div class="detail-content-modern">
                  <span class="detail-label-modern">Industry</span>
                  <span class="detail-value-modern">{{ employer.employer_industry }}</span>
                </div>
              </div>
              {% endif %}

              {% if employer.employer_headcount %}
              <div class="detail-item-modern">
                <div class="detail-icon-modern">
                  <i class="bi bi-people"></i>
                </div>
                <div class="detail-content-modern">
                  <span class="detail-label-modern">Team Size</span>
                  <span class="detail-value-modern">{{ employer.employer_headcount }}</span>
                </div>
              </div>
              {% endif %}

              {% if employer.headquarter %}
              <div class="detail-item-modern">
                <div class="detail-icon-modern">
                  <i class="bi bi-geo-alt"></i>
                </div>
                <div class="detail-content-modern">
                  <span class="detail-label-modern">Location</span>
                  <span class="detail-value-modern">{{ employer.headquarter }}</span>
                </div>
              </div>
              {% endif %}
            </div>
          </div>

          <!-- Card Footer -->
          <div class="employer-card-footer-modern">
            <div class="view-company-btn">
              <span>View Company</span>
              <i class="bi bi-arrow-right"></i>
            </div>
          </div>
        </a>
      </div>
      {% endfor %}
    </div>

    <!-- No Results Message -->
    {% if not all_employers %}
    <div class="no-employers-message">
      <div class="no-employers-icon">
        <i class="bi bi-building"></i>
      </div>
      <h3>No companies found</h3>
      <p>Try adjusting your search criteria or check back later for new companies!</p>
      <a href="/jobs" class="btn btn-primary">
        <i class="bi bi-briefcase me-2"></i>
        Explore Job Opportunities
      </a>
    </div>
    {% endif %}
  </div>
</section>

<!-- Call to Action Section -->
<section class="employers-cta-section py-5">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8 text-center">
        <div class="cta-content">
          <h2 class="cta-title mb-4">
            Ready to Find Your <span class="gradient-text">Dream Company</span>?
          </h2>
          <p class="cta-subtitle mb-4">
            Join thousands of professionals who have found their perfect workplace match through our platform.
          </p>
          <div class="cta-actions d-flex justify-content-center gap-3 flex-wrap">
            <a href="/jobs" class="btn btn-hero-primary btn-lg">
              <i class="bi bi-search me-2"></i>
              Browse All Jobs
            </a>
            <a href="/job-alerts" class="btn btn-hero-secondary btn-lg">
              <i class="bi bi-bell me-2"></i>
              Set Job Alerts
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{% endblock %}

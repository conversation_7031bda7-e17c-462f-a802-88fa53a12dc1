{% extends "base.html" %} {% block title %}Employers{% endblock %} {% block content %}

<!-- Simple Header Section -->
<section class="employers-header-section py-5">
  <div class="container">
    <div class="row">
      <div class="col-12 text-center">
        <h1 class="employers-title mb-3">
          Explore Top <span class="text-primary">Employers</span>
        </h1>
        <p class="employers-subtitle mb-4">
          Discover innovative organizations and find your perfect workplace match
        </p>

        <!-- Simple Stats -->
        <div class="employers-stats d-flex justify-content-center gap-4 mb-4">
          <div class="stat-item">
            <span class="stat-number">{{ all_employers|length }}+</span>
            <span class="stat-label">Companies</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ total_open_positions }}+</span>
            <span class="stat-label">Open Positions</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Simple Search Section -->
<section class="employers-search-section py-4">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8 col-md-10">
        <div class="search-card">
          <div class="form-floating">
            <input
              type="text"
              class="form-control border-2"
              id="keyword-input"
              name="keyword-input"
              placeholder="Search companies..."
              hx-get="/filteremp"
              hx-target="#employers-list"
              hx-trigger="keyup changed delay:300ms"
              hx-indicator="#search-loading"
            />
            <label for="keyword-input">
              <i class="bi bi-search me-2"></i>Search companies, industries, or locations...
            </label>
          </div>

          <!-- Search Loading Indicator -->
          <div class="text-center mt-3">
            <div id="search-loading" class="htmx-indicator">
              <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <span class="text-muted">Searching companies...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Companies Section -->
<section class="employers-listing-section py-5">
  <div class="container">

    <!-- Section Header -->
    <div class="section-header text-center mb-5">
      <h2 class="section-title">
        <span class="gradient-text">Featured Companies</span>
      </h2>
      <p class="section-subtitle">
        Explore opportunities with companies that are actively hiring and growing
      </p>
      <div class="section-divider"></div>
    </div>

    <!-- Employers Grid -->
    <div id="employers-list" class="row g-4">
      {% for employer in all_employers %}
      <div class="col-xl-4 col-lg-6 col-md-6 col-12">
        <div class="employer-card-minimal">
          <a href="{{url_for('employer',employer_id=employer.employer_id)}}" class="employer-link-minimal">
            <!-- Card Header -->
            <div class="employer-header-minimal">
              <div class="employer-logo-minimal">
                {% if employer.employer_logo_url %}
                <img
                  src="{{ employer.employer_logo_url }}"
                  class="company-logo-img"
                  alt="{{ employer.employer_name }}"
                  onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                />
                {% endif %}
                <div class="company-logo-fallback" {% if employer.employer_logo_url %}style="display: none"{% endif %}>
                  {{ (employer.employer_name or 'C')[0]|upper }}
                </div>
              </div>

              <!-- Open Positions Badge - Only animated element -->
              {% if employer.open_positions > 0 %}
              <div class="positions-badge-minimal">
                <i class="bi bi-briefcase me-1"></i>
                {{ employer.open_positions }}
              </div>
              {% endif %}
            </div>

            <!-- Company Info -->
            <div class="employer-content-minimal">
              <h3 class="company-name-minimal">{{ employer.employer_name }}</h3>

              <!-- Company Details -->
              <div class="company-details-minimal">
                {% if employer.employer_industry %}
                <div class="detail-row-minimal">
                  <i class="bi bi-diagram-3 text-muted me-2"></i>
                  <span class="detail-text">{{ employer.employer_industry }}</span>
                </div>
                {% endif %}

                {% if employer.employer_headcount %}
                <div class="detail-row-minimal">
                  <i class="bi bi-people text-muted me-2"></i>
                  <span class="detail-text">{{ employer.employer_headcount }}</span>
                </div>
                {% endif %}

                {% if employer.headquarter %}
                <div class="detail-row-minimal">
                  <i class="bi bi-geo-alt text-muted me-2"></i>
                  <span class="detail-text">{{ employer.headquarter }}</span>
                </div>
                {% endif %}
              </div>
            </div>

            <!-- Simple Footer -->
            <div class="employer-footer-minimal">
              <span class="view-text">View Company</span>
              <i class="bi bi-arrow-right"></i>
            </div>
          </a>
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- No Results Message -->
    {% if not all_employers %}
    <div class="row">
      <div class="col-12">
        <div class="no-results-card text-center py-5">
          <i class="bi bi-building text-muted mb-3" style="font-size: 3rem;"></i>
          <h4>No companies found</h4>
          <p class="text-muted">Try adjusting your search criteria or check back later for new companies!</p>
          <a href="/jobs" class="btn btn-primary">
            <i class="bi bi-briefcase me-2"></i>
            Explore Job Opportunities
          </a>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</section>

{% endblock %}

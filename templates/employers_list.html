{% for employer in all_employers %}
<div class="employer-card-ultra-modern" data-aos="fade-up" data-aos-delay="{{ loop.index0 * 100 }}">
  <a href="{{url_for('employer',employer_id=employer.employer_id)}}" class="employer-link">
    <!-- Card Header with Logo -->
    <div class="employer-card-header-modern">
      <div class="employer-logo-container-modern">
        {% if employer.employer_logo_url %}
        <img
          src="{{ employer.employer_logo_url }}"
          class="employer-logo-modern"
          alt="{{ employer.employer_name }}"
          onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
        />
        {% endif %}
        <div class="employer-logo-fallback-modern" {% if employer.employer_logo_url %}style="display: none"{% endif %}>
          {{ (employer.employer_name or 'C')[0]|upper }}
        </div>
      </div>

      <!-- Open Positions Badge -->
      {% if employer.open_positions > 0 %}
      <div class="positions-badge">
        <i class="bi bi-briefcase me-1"></i>
        {{ employer.open_positions }} {% if employer.open_positions == 1 %}Position{% else %}Positions{% endif %}
      </div>
      {% endif %}
    </div>

    <!-- Company Info -->
    <div class="employer-info-modern">
      <h3 class="company-name-modern">{{ employer.employer_name }}</h3>

      <!-- Company Details Grid -->
      <div class="company-details-grid">
        {% if employer.employer_industry %}
        <div class="detail-item-modern">
          <div class="detail-icon-modern">
            <i class="bi bi-diagram-3"></i>
          </div>
          <div class="detail-content-modern">
            <span class="detail-label-modern">Industry</span>
            <span class="detail-value-modern">{{ employer.employer_industry }}</span>
          </div>
        </div>
        {% endif %}

        {% if employer.employer_headcount %}
        <div class="detail-item-modern">
          <div class="detail-icon-modern">
            <i class="bi bi-people"></i>
          </div>
          <div class="detail-content-modern">
            <span class="detail-label-modern">Team Size</span>
            <span class="detail-value-modern">{{ employer.employer_headcount }}</span>
          </div>
        </div>
        {% endif %}

        {% if employer.headquarter %}
        <div class="detail-item-modern">
          <div class="detail-icon-modern">
            <i class="bi bi-geo-alt"></i>
          </div>
          <div class="detail-content-modern">
            <span class="detail-label-modern">Location</span>
            <span class="detail-value-modern">{{ employer.headquarter }}</span>
          </div>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- Card Footer -->
    <div class="employer-card-footer-modern">
      <div class="view-company-btn">
        <span>View Company</span>
        <i class="bi bi-arrow-right"></i>
      </div>
    </div>
  </a>
</div>
{% endfor %}

{% for employer in all_employers %}

<div class="col-xl-4 col-lg-6 col-md-6 col-12">
  <div class="employer-card modern-card">
    <div class="employer-card-header">
      <div class="employer-logo-section">
        <div class="employer-logo-wrapper">
          <img
            src="{{ employer.employer_logo_url }}"
            class="employer-icon-md"
            alt="{{ employer.employer_name }}"
            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
          />
          <div class="employer-logo-fallback" style="display: none;">
            {{ (employer.employer_name or 'C')[0]|upper }}
          </div>
        </div>
      </div>
      <div class="employer-jobs-badge">
        <span class="jobs-count">
          <i class="bi bi-briefcase me-1"></i>{{ employer.open_positions }} Jobs
        </span>
      </div>
    </div>

    <div class="company-details">
      <h6 class="fw-bold mb-3 py-1" style="font-size: 1.4rem">
        {{ employer.employer_name }}
      </h6>

      <div class="detail-item">
        <span class="detail-label"
          ><i class="bi bi-layout-text-window text-primary me-2"></i
          >Industry</span
        >
        <span class="detail-value">{{ employer.employer_industry }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label"
          ><i class="bi bi-people text-primary me-2"></i>Employees</span
        >
        <span class="detail-value">{{ employer.employer_headcount }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label"
          ><i class="bi bi-geo-alt text-primary me-2"></i>Location</span
        >
        <span class="detail-value">{{ employer.headquarter }}</span>
      </div>
    </div>

    <div class="card-footer bg-transparent border-0 pt-5 pb-3 pt-0">
      <a
        href="{{url_for('employer',employer_id=employer.employer_id)}}"
        class="btn btn-primary w-100 py-3 shadow-sm"
      >
        <i class="bi bi-eye me-2"></i>View Company Profile
      </a>
    </div>
  </div>
</div>
{% endfor %}

{% for employer in all_employers %}
<div class="col-xl-4 col-lg-6 col-md-6 col-12">
  <div class="employer-card-minimal">
    <a href="{{url_for('employer',employer_id=employer.employer_id)}}" class="employer-link-minimal">
      <!-- Card Header -->
      <div class="employer-header-minimal">
        <div class="employer-logo-minimal">
          {% if employer.employer_logo_url %}
          <img
            src="{{ employer.employer_logo_url }}"
            class="company-logo-img"
            alt="{{ employer.employer_name }}"
            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
          />
          {% endif %}
          <div class="company-logo-fallback" {% if employer.employer_logo_url %}style="display: none"{% endif %}>
            {{ (employer.employer_name or 'C')[0]|upper }}
          </div>
        </div>

        <!-- Open Positions Badge -->
        {% if employer.open_positions > 0 %}
        <div class="positions-badge-minimal">
          <i class="bi bi-briefcase me-1"></i>
          {{ employer.open_positions }}
        </div>
        {% endif %}
      </div>

      <!-- Company Info -->
      <div class="employer-content-minimal">
        <h3 class="company-name-minimal">{{ employer.employer_name }}</h3>

        <!-- Company Details -->
        <div class="company-details-minimal">
          {% if employer.employer_industry %}
          <div class="detail-row-minimal">
            <i class="bi bi-diagram-3 text-muted me-2"></i>
            <span class="detail-text">{{ employer.employer_industry }}</span>
          </div>
          {% endif %}

          {% if employer.employer_headcount %}
          <div class="detail-row-minimal">
            <i class="bi bi-people text-muted me-2"></i>
            <span class="detail-text">{{ employer.employer_headcount }}</span>
          </div>
          {% endif %}

          {% if employer.headquarter %}
          <div class="detail-row-minimal">
            <i class="bi bi-geo-alt text-muted me-2"></i>
            <span class="detail-text">{{ employer.headquarter }}</span>
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Simple Footer -->
      <div class="employer-footer-minimal">
        <span class="view-text">View Company</span>
        <i class="bi bi-arrow-right"></i>
      </div>
    </a>
  </div>
</div>
{% endfor %}

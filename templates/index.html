{% extends "base.html" %}
{% block title %} Jobsvider {% endblock %}
{% block content%}

<!-- Hero Section -->
<section class="modern-hero-bg py-5">
  <div class="container">
    <!-- Row 1: Title and Image -->
    <div class="row align-items-center mb-5">
      <div class="col-lg-7">
        <div class="hero-content-text">
          <h1 class="hero-title mb-4">
            Discover Your Next<br />
            <span class="hero-gradient-text">Dream Career</span>
          </h1>
          <p class="hero-subtitle mb-0">
            Connect with innovative companies, explore cutting-edge opportunities, and
            accelerate your professional journey. Join thousands of professionals
            finding their perfect match.
          </p>
        </div>
      </div>
      <div class="col-lg-5 d-none d-lg-block">
        <div class="hero-image-container">
          <img
            src="./static/undraw_file-search_cbur (2).svg"
            alt="Career opportunities"
            class="hero-image"
          />
          <div class="floating-elements">
            <div class="floating-card floating-card-1">
              <i class="bi bi-briefcase"></i>
              <span>Remote Work</span>
            </div>
            <div class="floating-card floating-card-2">
              <i class="bi bi-graph-up"></i>
              <span>Growth</span>
            </div>
            <div class="floating-card floating-card-3">
              <i class="bi bi-people"></i>
              <span>Team</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 2: Search Bar -->
    <div class="row justify-content-center mb-5">
      <div class="col-lg-12">
        <div class="hero-search-container">
          <form class="hero-search-form" action="/jobs" method="GET">
            <div class="hero-search-wrapper">
              <div class="hero-search-input-group">
                <i class="bi bi-search hero-search-icon"></i>
                <input
                  type="text"
                  name="keyword-input"
                  class="hero-search-input"
                  placeholder="Search for jobs, skills, or companies..."
                  autocomplete="off"
                />
                <button type="submit" class="hero-search-btn">
                  <i class="bi bi-arrow-right"></i>
                </button>
              </div>
              <div class="hero-search-suggestions">
                <span class="search-suggestion" data-keyword="Software Engineer">Software Engineer</span>
                <span class="search-suggestion" data-keyword="Product Manager">Product Manager</span>
                <span class="search-suggestion" data-keyword="Data Scientist">Data Scientist</span>
                <span class="search-suggestion" data-keyword="UX Designer">UX Designer</span>
                <span class="search-suggestion" data-keyword="Marketing Manager">Marketing Manager</span>
                <span class="search-suggestion" data-keyword="DevOps Engineer">DevOps Engineer</span>
                <span class="search-suggestion" data-keyword="Business Analyst">Business Analyst</span>
                <span class="search-suggestion" data-keyword="Remote">Remote</span>
                <span class="search-suggestion" data-keyword="Full Stack Developer">Full Stack Developer</span>
                <span class="search-suggestion" data-keyword="Frontend Developer">Frontend Developer</span>
                <span class="search-suggestion" data-keyword="Backend Developer">Backend Developer</span>
                <span class="search-suggestion" data-keyword="QA Engineer">QA Engineer</span>
                <span class="search-suggestion" data-keyword="Cybersecurity Analyst">Cybersecurity Analyst</span>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <hr class="mt-5"/>

    <!-- Row 3: Actions and Stats -->
    <div class="row align-items-center">
      <div class="col-lg-8">
        <div class="hero-actions d-flex align-items-center justify-content-center gap-3">
          <a href="/jobs" class="btn btn-hero-primary btn-lg px-5 py-3">
            <i class="bi bi-rocket-takeoff me-2"></i>
            Explore All Opportunities
          </a>
          <a href="/job-alerts" class="btn btn-hero-secondary btn-lg px-5 py-3">
            <i class="bi bi-bell me-2"></i>
            Get Job Alerts
          </a>
        </div>
      </div>
      <div class="col-lg-4">
        <div class="hero-stats d-flex flex-wrap gap-4 justify-content-lg-start justify-content-center mt-4 mt-lg-0">
          <div class="stat-item">
            <div class="stat-number">{{ total_jobs }}+</div>
            <div class="stat-label">Active Jobs</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ total_companies }}+</div>
            <div class="stat-label">Companies</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">Countless</div>
            <div class="stat-label">Happy Candidates</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Latest Opportunities Section -->
<section class="latest-opportunities py-5">
  <div class="container">
    <div class="section-header text-center mb-5">
      <h2 class="section-title">
        Latest <span class="gradient-text ms-2"> Opportunities </span>
      </h2>
      <p class="section-subtitle">
        Discover fresh job openings from top companies worldwide
      </p>
      <div class="section-divider"></div>
    </div>

    <div class="jobs-grid">
      {% for vacancy in all_recent_jobs[:15] %}
      <div class="job-card-modern" data-aos="fade-up" data-aos-delay="{{ loop.index0 * 50 }}">
        <a
          href="{% if vacancy.listing_source == 'RSS' %}{{ vacancy.vacancy_url }}{% else %}{{ url_for('view_vacancy', vacancy_id=vacancy.vacancy_id) }}{% endif %}"
          class="job-link"
          {% if vacancy.listing_source == 'RSS' %}target="_blank"{% endif %}
        >
          <div class="job-card-content">
            <!-- Company Logo & Header -->
            <div class="job-header">
              <div class="company-logo-wrapper">
                {% if vacancy.employer_logo_url %}
                <img
                  src="{{ vacancy.employer_logo_url }}"
                  alt="{{ vacancy.employer_name or 'Company' }} logo"
                  class="company-logo-modern-sm"
                  onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                />
                {% endif %}
                <div class="company-logo-fallback" style="display: {% if not vacancy.employer_logo_url %}flex{% else %}none{% endif %}; align-items: center; justify-content: center; width: 50px; height: 50px; background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%); border-radius: 8px; color: white; font-weight: 600; font-size: 1.2rem;">
                  {{ (vacancy.employer_name or 'C')[0]|upper }}
                </div>
              </div>
              {% if vacancy.listing_source == 'RSS' %}
              <div class="job-source-badge">
                <i class="bi bi-rss"></i>
                <span>Featured</span>
              </div>
              {% endif %}
            </div>

            <!-- Job Title & Company -->
            <div class="job-info">
              <h3 class="job-title">{{ vacancy.vacancy_title or 'Job Title' }}</h3>
              <p class="company-name">{{ vacancy.employer_name or 'Company Name' }}</p>
            </div>

            <!-- Job Tags -->
            <div class="job-tags">
              {% if vacancy.office_schedule %}
              <span class="job-tag job-tag-primary">
                <i class="bi bi-{{ 'house' if 'Remote' in (vacancy.office_schedule or '') else 'building' }}"></i>
                {{ vacancy.office_schedule }}
              </span>
              {% endif %}
              {% if vacancy.vacancy_country %}
              <span class="job-tag job-tag-secondary">
                <i class="bi bi-geo-alt"></i>
                {{ vacancy.vacancy_country }}
              </span>
              {% endif %}
              {% if vacancy.vacancy_type %}
              <span class="job-tag job-tag-accent">
                <i class="bi bi-clock"></i>
                {{ vacancy.vacancy_type }}
              </span>
              {% elif vacancy.listing_source == 'RSS' %}
              <span class="job-tag job-tag-accent">
                <i class="bi bi-star"></i>
                External
              </span>
              {% endif %}
            </div>

            <!-- Salary & Date -->
            <div class="job-footer">
              <div class="salary-info">
                {% if vacancy.salary_min and vacancy.salary_max %}
                <div class="salary-range">
                  {% set min_salary = vacancy.salary_min|string %}
                  {% set max_salary = vacancy.salary_max|string %}
                  {{ vacancy.salary_currency or '$' }} {{ min_salary }} - {{ max_salary }}
                </div>
                <div class="salary-period">per month</div>
                {% else %}
                <div class="salary-competitive">Competitive Salary</div>
                {% endif %}
              </div>
              <div class="job-date">
                <i class="bi bi-clock"></i>
                {{ vacancy.vacancy_age or 'Recently posted' }}
              </div>
            </div>
          </div>
        </a>
      </div>
      {% endfor %}
    </div>

    <!-- Load More Button -->
    <div class="text-center mt-5">
      <a href="/jobs" class="btn btn-load-more">
        <i class="bi bi-plus-circle me-2"></i>
        View All {{ total_jobs }}+ Opportunities
      </a>
    </div>

    <!-- Show message if no jobs -->
    {% if not all_recent_jobs %}
    <div class="no-jobs-message">
      <div class="no-jobs-icon">
        <i class="bi bi-briefcase"></i>
      </div>
      <h3>No opportunities available right now</h3>
      <p>New jobs are added daily. Check back soon or set up job alerts to be notified!</p>
      <a href="/job-alerts" class="btn btn-primary">
        <i class="bi bi-bell me-2"></i>
        Set Up Job Alerts
      </a>
    </div>
    {% endif %}
  </div>
</section>

<!-- Hero Search JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.hero-search-input');
    const searchSuggestions = document.querySelectorAll('.search-suggestion');
    const searchForm = document.querySelector('.hero-search-form');

    // Handle suggestion clicks
    searchSuggestions.forEach(suggestion => {
        suggestion.addEventListener('click', function() {
            const keyword = this.getAttribute('data-keyword');
            searchInput.value = keyword;

            // Animate the suggestion
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-2px)';
            }, 150);

            // Submit the form after a short delay
            setTimeout(() => {
                searchForm.submit();
            }, 300);
        });
    });

    // Handle form submission
    searchForm.addEventListener('submit', function(e) {
        const keyword = searchInput.value.trim();
        if (!keyword) {
            e.preventDefault();
            searchInput.focus();
            return;
        }

        // Add loading state to search button
        const searchBtn = document.querySelector('.hero-search-btn');
        const originalContent = searchBtn.innerHTML;
        searchBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
        searchBtn.style.animation = 'spin 1s linear infinite';

        // Construct the URL with proper parameter name
        const url = new URL('/jobs', window.location.origin);
        url.searchParams.set('keyword-input', keyword);

        // Navigate to the jobs page with search parameter
        window.location.href = url.toString();

        // Prevent default form submission
        e.preventDefault();
    });

    // Add focus effects
    searchInput.addEventListener('focus', function() {
        this.parentElement.style.transform = 'scale(1.02)';
    });

    searchInput.addEventListener('blur', function() {
        this.parentElement.style.transform = 'scale(1)';
    });
});

// Add spin animation for loading state
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>

{% endblock %}
